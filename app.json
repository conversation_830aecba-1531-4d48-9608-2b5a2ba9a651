{"expo": {"name": "NutKhut", "slug": "NutKhut", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/logo2.jpeg", "scheme": "nutkhut", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"bundleIdentifier": "com.nutkhut.app", "supportsTablet": true}, "android": {"package": "com.nutkhut.app", "adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "edgeToEdgeEnabled": true}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/splash-icon.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#ffffff"}]], "experiments": {"typedRoutes": true}}}
import { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Alert,
  Modal,
  RefreshControl,
  ActivityIndicator,
  useWindowDimensions,
  Platform,
  KeyboardAvoidingView,
  Animated,
  Easing,
} from 'react-native';
import { useAuth } from '../../hooks/useAuth';
import { FontAwesome } from '@expo/vector-icons';
import { firestore } from '../../config/firebase';
import { collection, addDoc, getDocs, doc, updateDoc, query, orderBy, deleteDoc } from 'firebase/firestore';

interface Student {
  id: string;
  name: string;
  class: string;
  rollNumber: string;
  parentName: string;
  parentEmail: string;
  parentPhone: string;
  admissionDate: string;
  status: 'active' | 'inactive';
  address: string;
}

export default function AdminStudents() {
  const { width } = useWindowDimensions();
  const { user } = useAuth();
  const [searchQuery, setSearchQuery] = useState('');
  const [showAddModal, setShowAddModal] = useState(false);
  const [selectedStudent, setSelectedStudent] = useState<Student | null>(null);
  const [showModal, setShowModal] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [studentToDelete, setStudentToDelete] = useState<Student | null>(null);

  // Real data from Firestore
  const [students, setStudents] = useState<Student[]>([]);

  // Fetch students from Firestore
  const fetchStudents = async () => {
    try {
      console.log('Fetching students from Firestore...');
      setRefreshing(true);
      const studentsCollection = collection(firestore, 'students');
      const studentsQuery = query(studentsCollection, orderBy('name', 'asc'));
      const querySnapshot = await getDocs(studentsQuery);

      console.log(`Found ${querySnapshot.size} students in Firestore`);

      const studentsData: Student[] = [];
      querySnapshot.forEach((doc) => {
        const data = doc.data();
        console.log('Student data:', { id: doc.id, ...data });
        studentsData.push({
          id: doc.id,
          ...data as Omit<Student, 'id'>
        });
      });

      console.log('Setting students state with:', studentsData);
      setStudents(studentsData);
    } catch (error) {
      console.error('Error fetching students:', error);
      Alert.alert('Error', 'Failed to load students. Please try again.');
    } finally {
      setRefreshing(false);
    }
  };

  const [newStudent, setNewStudent] = useState<Partial<Student>>({
    name: '',
    class: '',
    rollNumber: '',
    parentName: '',
    parentEmail: '',
    parentPhone: '',
    address: '',
  });

  const filteredStudents = students.filter(
    (student) =>
      student.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      student.rollNumber.toLowerCase().includes(searchQuery.toLowerCase()) ||
      student.parentName.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleAddStudent = async () => {
    if (
      !newStudent.name ||
      !newStudent.class ||
      !newStudent.rollNumber ||
      !newStudent.parentName ||
      !newStudent.parentEmail ||
      !newStudent.parentPhone ||
      !newStudent.address
    ) {
      Alert.alert('Error', 'Please fill all required fields');
      return;
    }

    setIsSubmitting(true);
    try {
      if (isEditing && selectedStudent) {
        console.log('Updating student:', selectedStudent.id);

        // Update in Firestore
        const studentRef = doc(firestore, 'students', selectedStudent.id);
        await updateDoc(studentRef, {
          ...newStudent,
          // Keep the original admission date and status
        });
        console.log('Student updated successfully');

        // Reset editing state
        setIsEditing(false);
        setShowAddModal(false);
        Alert.alert('Success', 'Student updated successfully');
      } else {
        console.log('Adding new student:', newStudent);

        // Create student object
        const studentData = {
          ...newStudent,
          admissionDate: new Date().toISOString().split('T')[0],
          status: 'active',
          deactivationDate: null, // Add this field for tracking deactivation date
        };

        // Add to Firestore
        console.log('Adding to Firestore collection: students');
        const studentsCollection = collection(firestore, 'students');
        const docRef = await addDoc(studentsCollection, studentData);
        console.log('Student added with ID:', docRef.id);

        setShowAddModal(false);
        Alert.alert('Success', 'Student added successfully');
      }

      // Reset form
      setNewStudent({
        name: '',
        class: '',
        rollNumber: '',
        parentName: '',
        parentEmail: '',
        parentPhone: '',
        address: '',
      });

      // Refresh the student list
      console.log('Refreshing student list...');
      await fetchStudents();
    } catch (error) {
      Alert.alert('Error', 'Failed to add student');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDeleteStudent = (student: Student) => {
    console.log('Delete button pressed for student:', student);
    setStudentToDelete(student);
    setShowDeleteModal(true);
  };

  const onRefresh = async () => {
    await fetchStudents();
  };

  // Load students when component mounts
  useEffect(() => {
    fetchStudents();
  }, []);

  const isSmallScreen = width < 380;

  return (
    <View className="flex-1 bg-gray-50">
      {/* Header */}
      <View className={`bg-white px-4 ${isSmallScreen ? 'py-4' : 'py-6'} border-b border-gray-200`}>
        <Text className={`${isSmallScreen ? 'text-xl' : 'text-2xl'} font-bold text-gray-800`}>
          Students
        </Text>
        <Text className="text-sm text-gray-500 mt-1">
          Manage student information and records
        </Text>
      </View>

      {/* Search Bar */}
      <View className="p-4 bg-white border-b border-gray-200">
        <View className="flex-row items-center bg-gray-100 px-4 py-2 rounded-xl">
          <FontAwesome name="search" size={16} color="#6B7280" />
          <TextInput
            className="flex-1 ml-2 text-base text-gray-900"
            placeholder="Search students..."
            value={searchQuery}
            onChangeText={setSearchQuery}
            placeholderTextColor="#9CA3AF"
          />
          {searchQuery !== '' && (
            <TouchableOpacity
              onPress={() => setSearchQuery('')}
              className="p-1"
            >
              <FontAwesome name="times-circle" size={16} color="#6B7280" />
            </TouchableOpacity>
          )}
        </View>
      </View>

      {/* Student List */}
      <ScrollView
        className="flex-1"
        contentContainerStyle={{ paddingBottom: 120 }}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        <View className={`p-${isSmallScreen ? '3' : '4'} space-y-${isSmallScreen ? '3' : '4'}`}>
          {filteredStudents.map((student) => (
            <TouchableOpacity
              key={student.id}
              className="bg-white rounded-xl p-4 shadow-sm border border-gray-100"
              onPress={() => {
                setSelectedStudent(student);
                setShowModal(true);
              }}
              activeOpacity={0.7}
            >
              <View className="flex-row justify-between items-start">
                <View className="flex-1 mr-3">
                  <Text className={`${isSmallScreen ? 'text-base' : 'text-lg'} font-semibold text-gray-800`}>
                    {student.name}
                  </Text>
                  <Text className="text-sm text-gray-500">
                    Class {student.class}
                  </Text>
                  <Text className="text-sm text-gray-500 mt-1">
                    Roll No: {student.rollNumber}
                  </Text>
                </View>
                <View className={`px-3 py-1 rounded-full ${
                  student.status === 'active'
                    ? 'bg-green-100 text-green-800'
                    : 'bg-red-100 text-red-800'
                }`}>
                  <Text className="text-xs font-medium capitalize">
                    {student.status}
                  </Text>
                </View>
              </View>
            </TouchableOpacity>
          ))}
        </View>
      </ScrollView>

      {/* Add Student Button */}
      <TouchableOpacity
        className="absolute right-6 bg-blue-500 rounded-full items-center justify-center shadow-xl"
        style={{
          bottom: 110, // Ensure it's above the tab bar
          width: 65,
          height: 65,
          elevation: 8,
          shadowColor: '#3B82F6',
          shadowOffset: { width: 0, height: 4 },
          shadowOpacity: 0.3,
          shadowRadius: 6,
          zIndex: 1000,
        }}
        onPress={() => setShowAddModal(true)}
        activeOpacity={0.7}
      >
        <FontAwesome name="plus" size={28} color="white" />
      </TouchableOpacity>

      {/* Add Student Modal */}
      <Modal
        visible={showAddModal}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setShowAddModal(false)}
      >
        <KeyboardAvoidingView
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          className="flex-1"
        >
          <View className="flex-1 bg-black/50 justify-end">
            <View className="bg-white rounded-t-3xl p-6">
              <View className="flex-row justify-between items-center mb-6">
                <Text className={`${isSmallScreen ? 'text-lg' : 'text-xl'} font-bold text-gray-800`}>
                  Add New Student
                </Text>
                <TouchableOpacity
                  onPress={() => setShowAddModal(false)}
                  className="p-2"
                >
                  <FontAwesome name="close" size={24} color="#6B7280" />
                </TouchableOpacity>
              </View>

              <ScrollView className="max-h-[60vh]">
                <View className="space-y-4">
                  <View>
                    <Text className="text-sm font-medium text-gray-700 mb-1">
                      Student Name *
                    </Text>
                    <TextInput
                      className="w-full bg-gray-50 border border-gray-200 rounded-xl px-4 py-3 text-gray-900"
                      placeholder="Enter student name"
                      value={newStudent.name}
                      onChangeText={(text) => setNewStudent({ ...newStudent, name: text })}
                      placeholderTextColor="#9CA3AF"
                    />
                  </View>

                  <View className="flex-row space-x-4">
                    <View className="flex-1">
                      <Text className="text-sm font-medium text-gray-700 mb-1">
                        Class *
                      </Text>
                      <TextInput
                        className="w-full bg-gray-50 border border-gray-200 rounded-xl px-4 py-3 text-gray-900"
                        placeholder="Enter class"
                        value={newStudent.class}
                        onChangeText={(text) => setNewStudent({ ...newStudent, class: text })}
                        placeholderTextColor="#9CA3AF"
                      />
                    </View>
                    <View className="flex-1">
                      <Text className="text-sm font-medium text-gray-700 mb-1">
                        Roll Number *
                      </Text>
                      <TextInput
                        className="w-full bg-gray-50 border border-gray-200 rounded-xl px-4 py-3 text-gray-900"
                        placeholder="Enter roll number"
                        value={newStudent.rollNumber}
                        onChangeText={(text) => setNewStudent({ ...newStudent, rollNumber: text })}
                        placeholderTextColor="#9CA3AF"
                      />
                    </View>
                  </View>

                  <View>
                    <Text className="text-sm font-medium text-gray-700 mb-1">
                      Parent Name *
                    </Text>
                    <TextInput
                      className="w-full bg-gray-50 border border-gray-200 rounded-xl px-4 py-3 text-gray-900"
                      placeholder="Enter parent name"
                      value={newStudent.parentName}
                      onChangeText={(text) => setNewStudent({ ...newStudent, parentName: text })}
                      placeholderTextColor="#9CA3AF"
                    />
                  </View>

                  <View>
                    <Text className="text-sm font-medium text-gray-700 mb-1">
                      Parent Email *
                    </Text>
                    <TextInput
                      className="w-full bg-gray-50 border border-gray-200 rounded-xl px-4 py-3 text-gray-900"
                      placeholder="Enter parent email"
                      value={newStudent.parentEmail}
                      onChangeText={(text) => setNewStudent({ ...newStudent, parentEmail: text })}
                      keyboardType="email-address"
                      autoCapitalize="none"
                      placeholderTextColor="#9CA3AF"
                    />
                  </View>

                  <View>
                    <Text className="text-sm font-medium text-gray-700 mb-1">
                      Parent Phone *
                    </Text>
                    <TextInput
                      className="w-full bg-gray-50 border border-gray-200 rounded-xl px-4 py-3 text-gray-900"
                      placeholder="Enter parent phone"
                      value={newStudent.parentPhone}
                      onChangeText={(text) => setNewStudent({ ...newStudent, parentPhone: text })}
                      keyboardType="phone-pad"
                      placeholderTextColor="#9CA3AF"
                    />
                  </View>

                  <View>
                    <Text className="text-sm font-medium text-gray-700 mb-1">
                      Address *
                    </Text>
                    <TextInput
                      className="w-full bg-gray-50 border border-gray-200 rounded-xl px-4 py-3 text-gray-900"
                      placeholder="Enter address"
                      value={newStudent.address}
                      onChangeText={(text) => setNewStudent({ ...newStudent, address: text })}
                      multiline
                      numberOfLines={3}
                      placeholderTextColor="#9CA3AF"
                    />
                  </View>
                </View>
              </ScrollView>

              <TouchableOpacity
                className="w-full bg-blue-500 p-4 rounded-xl mt-6 opacity-90 active:opacity-100"
                onPress={handleAddStudent}
                disabled={isSubmitting}
              >
                {isSubmitting ? (
                  <ActivityIndicator color="white" />
                ) : (
                  <Text className="text-white text-center font-semibold">
                    Add Student
                  </Text>
                )}
              </TouchableOpacity>
            </View>
          </View>
        </KeyboardAvoidingView>
      </Modal>

      {/* Student Detail Modal */}
      <Modal
        visible={showModal}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setShowModal(false)}
      >
        <View className="flex-1 bg-black/50 justify-end">
          <View className={`bg-white rounded-t-3xl p-${isSmallScreen ? '5' : '6'} h-[85%]`}>
            <View className="flex-row justify-between items-center mb-6">
              <Text className="text-2xl font-bold text-gray-800">
                Student Details
              </Text>
              <TouchableOpacity
                onPress={() => setShowModal(false)}
                className="p-2 bg-gray-100 rounded-full"
              >
                <FontAwesome name="close" size={24} color="#4B5563" />
              </TouchableOpacity>
            </View>

            {selectedStudent && (
              <ScrollView className="flex-1">
                <View className="space-y-6">
                  {/* Basic Info */}
                  <View>
                    <Text className="text-base font-bold text-gray-700 mb-3">
                      Basic Information
                    </Text>
                    <View className="bg-gray-50 p-5 rounded-xl space-y-4 shadow-sm">
                      <View>
                        <Text className="text-gray-500 text-sm mb-1">Full Name</Text>
                        <Text className="text-xl font-semibold text-gray-800">
                          {selectedStudent.name}
                        </Text>
                      </View>
                      <View className="flex-row space-x-6">
                        <View className="flex-1">
                          <Text className="text-gray-500 text-sm mb-1">Class</Text>
                          <Text className="text-lg font-medium text-gray-800">
                            {selectedStudent.class}
                          </Text>
                        </View>
                        <View className="flex-1">
                          <Text className="text-gray-500 text-sm mb-1">Roll Number</Text>
                          <Text className="text-lg font-medium text-gray-800">
                            {selectedStudent.rollNumber}
                          </Text>
                        </View>
                      </View>
                      <View>
                        <Text className="text-gray-500 text-sm mb-1">Admission Date</Text>
                        <Text className="text-lg font-medium text-gray-800">
                          {new Date(selectedStudent.admissionDate).toLocaleDateString()}
                        </Text>
                      </View>
                      <View>
                        <Text className="text-gray-500 text-sm mb-1">Status</Text>
                        <View className={`px-4 py-2 rounded-lg mt-1 ${
                          selectedStudent.status === 'active'
                            ? 'bg-green-100'
                            : 'bg-red-100'
                        }`}>
                          <Text className={`text-base font-bold capitalize ${
                            selectedStudent.status === 'active'
                              ? 'text-green-700'
                              : 'text-red-700'
                          }`}>
                            {selectedStudent.status}
                          </Text>
                        </View>
                      </View>
                    </View>
                  </View>

                  {/* Parent Info */}
                  <View>
                    <Text className="text-base font-bold text-gray-700 mb-3">
                      Parent Information
                    </Text>
                    <View className="bg-gray-50 p-5 rounded-xl space-y-4 shadow-sm">
                      <View>
                        <Text className="text-gray-500 text-sm mb-1">Parent Name</Text>
                        <Text className="text-lg font-medium text-gray-800">
                          {selectedStudent.parentName}
                        </Text>
                      </View>
                      <View className="flex-row space-x-6">
                        <View className="flex-1">
                          <Text className="text-gray-500 text-sm mb-1">Email</Text>
                          <Text className="text-lg font-medium text-gray-800">
                            {selectedStudent.parentEmail}
                          </Text>
                        </View>
                        <View className="flex-1">
                          <Text className="text-gray-500 text-sm mb-1">Phone Number</Text>
                          <Text className="text-lg font-medium text-gray-800">
                            {selectedStudent.parentPhone}
                          </Text>
                        </View>
                      </View>
                      <View>
                        <Text className="text-gray-500 text-sm mb-1">Address</Text>
                        <Text className="text-lg font-medium text-gray-800">
                          {selectedStudent.address}
                        </Text>
                      </View>
                    </View>
                  </View>

                  {/* Action Buttons */}
                  <View className="mt-8 mb-4 space-y-4">
                    <TouchableOpacity
                      className="bg-blue-500 p-5 rounded-lg shadow-md w-full"
                      style={{
                        elevation: 4,
                        shadowColor: '#3B82F6',
                        shadowOffset: { width: 0, height: 3 },
                        shadowOpacity: 0.3,
                        shadowRadius: 4,
                      }}
                      activeOpacity={0.7}
                      onPress={() => {
                        setNewStudent({
                          name: selectedStudent.name,
                          class: selectedStudent.class,
                          rollNumber: selectedStudent.rollNumber,
                          parentName: selectedStudent.parentName,
                          parentEmail: selectedStudent.parentEmail,
                          parentPhone: selectedStudent.parentPhone,
                          address: selectedStudent.address,
                        });
                        setShowModal(false);
                        setIsEditing(true);
                        setShowAddModal(true);
                      }}
                    >
                      <Text className="text-white text-center font-bold text-lg">
                        Edit Details
                      </Text>
                      <View className="absolute right-5">
                        <FontAwesome name="edit" size={20} color="white" />
                      </View>
                    </TouchableOpacity>
                    <TouchableOpacity
                      className="flex-1 bg-red-500 p-5 rounded-lg shadow-md"
                      style={{
                        elevation: 3,
                        shadowColor: '#EF4444',
                        shadowOffset: { width: 0, height: 2 },
                        shadowOpacity: 0.2,
                        shadowRadius: 3,
                      }}
                      activeOpacity={0.8}
                      onPress={() => handleDeleteStudent(selectedStudent)}
                      disabled={isLoading}
                    >
                      {isLoading ? (
                        <ActivityIndicator color="white" size="small" />
                      ) : (
                        <Text className="text-white text-center font-bold text-base">
                          Delete
                        </Text>
                      )}
                    </TouchableOpacity>
                  </View>
                </View>
              </ScrollView>
            )}
          </View>
        </View>
      </Modal>

      {/* Custom Delete Confirmation Modal */}
      <Modal
        visible={showDeleteModal}
        transparent
        animationType="fade"
        onRequestClose={() => setShowDeleteModal(false)}
      >
        <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: 'rgba(0,0,0,0.4)' }}>
          <View style={{ backgroundColor: 'white', padding: 24, borderRadius: 12, width: 300 }}>
            <Text style={{ fontWeight: 'bold', fontSize: 18, marginBottom: 12 }}>Confirm Delete</Text>
            <Text style={{ marginBottom: 24 }}>
              Are you sure you want to delete {studentToDelete?.name}? This action cannot be undone.
            </Text>
            <View style={{ flexDirection: 'row', justifyContent: 'flex-end' }}>
              <TouchableOpacity onPress={() => setShowDeleteModal(false)} style={{ marginRight: 16 }}>
                <Text style={{ color: '#2563eb', fontWeight: 'bold' }}>Cancel</Text>
              </TouchableOpacity>
              <TouchableOpacity
                onPress={async () => {
                  if (!studentToDelete) return;
                  setIsLoading(true);
                  try {
                    const studentRef = doc(firestore, 'students', studentToDelete.id);
                    console.log('Attempting to delete doc at:', studentRef.path);
                    await deleteDoc(studentRef);
                    console.log('DeleteDoc finished');
                    await fetchStudents();
                    setShowModal(false);
                    setSelectedStudent(null);
                  } catch (error) {
                    console.error('Delete error:', error);
                    Alert.alert('Error', 'Failed to delete student');
                  } finally {
                    setIsLoading(false);
                    setShowDeleteModal(false);
                    setStudentToDelete(null);
                  }
                }}
              >
                <Text style={{ color: '#ef4444', fontWeight: 'bold' }}>Delete</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
}
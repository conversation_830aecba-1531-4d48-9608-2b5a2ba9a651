import { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Alert,
  Modal,
  ActivityIndicator,
  Animated,
  Easing,
} from 'react-native';
import { useAuth } from '../../hooks/useAuth';
import { FontAwesome } from '@expo/vector-icons';
import { Calendar, DateData } from 'react-native-calendars';
import { firestore } from '../../config/firebase';
import { collection, addDoc, getDocs, deleteDoc, doc, query, orderBy, updateDoc } from 'firebase/firestore';

interface Event {
  id: string;
  title: string;
  description: string;
  date: string;
  type: 'holiday' | 'exam' | 'activity' | 'other';
  targetAudience: 'all' | 'parents' | 'teachers' | 'specific_class';
  specificClass?: string;
}

export default function AdminCalendar() {
  // Get current user
  const { user } = useAuth();
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0]);
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEventModal, setShowEventModal] = useState(false);
  const [selectedEvent, setSelectedEvent] = useState<Event | null>(null);
  const [events, setEvents] = useState<Event[]>([]);
  const [loading, setLoading] = useState(true);

  // Animation for FAB
  const scaleAnim = useRef(new Animated.Value(1)).current;
  const rotateAnim = useRef(new Animated.Value(0)).current;

  // Animation functions
  const startPulseAnimation = () => {
    Animated.loop(
      Animated.sequence([
        Animated.timing(scaleAnim, {
          toValue: 1.15,
          duration: 800,
          easing: Easing.inOut(Easing.ease),
          useNativeDriver: true,
        }),
        Animated.timing(scaleAnim, {
          toValue: 1,
          duration: 800,
          easing: Easing.inOut(Easing.ease),
          useNativeDriver: true,
        }),
      ])
    ).start();
  };

  // Start animation when component mounts
  useEffect(() => {
    startPulseAnimation();
  }, []);

  const [newEvent, setNewEvent] = useState<Partial<Event>>({
    title: '',
    description: '',
    date: selectedDate,
    type: 'activity',
    targetAudience: 'all',
  });

  // State for editing events
  const [isEditing, setIsEditing] = useState(false);

  // Add state to track if we should show the class input
  const [showClassInput, setShowClassInput] = useState(false);

  const [showDatePicker, setShowDatePicker] = useState(false);

  // Fetch events from Firestore
  useEffect(() => {
    fetchEvents();
  }, []);

  const fetchEvents = async () => {
    try {
      setLoading(true);
      const eventsCollection = collection(firestore, 'events');
      const eventsQuery = query(eventsCollection, orderBy('date', 'asc'));
      const querySnapshot = await getDocs(eventsQuery);

      const eventsData: Event[] = [];
      querySnapshot.forEach((doc) => {
        const data = doc.data() as Omit<Event, 'id'>;
        eventsData.push({
          id: doc.id,
          ...data
        });
      });

      setEvents(eventsData);
    } catch (error) {
      console.error('Error fetching events:', error);
      Alert.alert('Error', 'Failed to load events. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Helper functions for event management

  function getEventTypeColor(type: string): string {
    switch (type) {
      case 'holiday':
        return '#EF4444';
      case 'exam':
        return '#F59E0B';
      case 'activity':
        return '#10B981';
      default:
        return '#6B7280';
    }
  }

  const handleAddEvent = async () => {
    if (!newEvent.title || !newEvent.description || !newEvent.date) {
      Alert.alert('Error', 'Please fill all required fields');
      return;
    }

    // Validate specific class is provided when target audience is specific_class
    if (newEvent.targetAudience === 'specific_class' && !newEvent.specificClass) {
      Alert.alert('Error', 'Please enter a class name');
      return;
    }

    try {
      setLoading(true);

      // Create event object without ID (Firestore will generate one)
      // Use Indian timezone (IST is UTC+5:30) for dates
      const now = new Date();
      const istOffset = 5.5 * 60 * 60 * 1000; // 5.5 hours in milliseconds
      const istDate = new Date(now.getTime() + istOffset);

      const eventData: any = {
        title: newEvent.title || '',
        description: newEvent.description || '',
        date: newEvent.date || selectedDate,
        type: newEvent.type || 'activity',
        targetAudience: newEvent.targetAudience || 'all',
        createdBy: user?.id || 'unknown',
        createdAt: istDate.toISOString(),
      };

      // Only add specificClass if it has a value
      if (newEvent.specificClass) {
        eventData.specificClass = newEvent.specificClass;
      }

      // Add to Firestore
      const eventsCollection = collection(firestore, 'events');
      await addDoc(eventsCollection, eventData);

      // Reset form
      setNewEvent({
        title: '',
        description: '',
        date: selectedDate,
        type: 'activity',
        targetAudience: 'all',
        specificClass: undefined, // Clear the specific class
      });

      // Reset the class input visibility
      setShowClassInput(false);

      setShowAddModal(false);
      Alert.alert('Success', 'Event added successfully');

      // Refresh events list
      fetchEvents();
    } catch (error) {
      console.error('Error adding event:', error);
      Alert.alert('Error', 'Failed to add event. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleEditEvent = (event: Event) => {
    // Set the form data for editing
    setNewEvent({
      title: event.title,
      description: event.description,
      date: event.date,
      type: event.type,
      targetAudience: event.targetAudience,
      specificClass: event.specificClass,
    });

    // Set editing mode
    setIsEditing(true);

    // Show the add/edit modal
    setShowEventModal(false);
    setShowAddModal(true);
  };

  const handleUpdateEvent = async () => {
    if (!newEvent.title || !newEvent.description || !newEvent.date) {
      Alert.alert('Error', 'Please fill all required fields');
      return;
    }

    // Validate specific class is provided when target audience is specific_class
    if (newEvent.targetAudience === 'specific_class' && !newEvent.specificClass) {
      Alert.alert('Error', 'Please enter a class name');
      return;
    }

    try {
      setLoading(true);

      // Create event object for update
      // Use Indian timezone (IST is UTC+5:30) for dates
      const now = new Date();
      const istOffset = 5.5 * 60 * 60 * 1000; // 5.5 hours in milliseconds
      const istDate = new Date(now.getTime() + istOffset);

      const eventData: any = {
        title: newEvent.title || '',
        description: newEvent.description || '',
        date: newEvent.date || selectedDate,
        type: newEvent.type || 'activity',
        targetAudience: newEvent.targetAudience || 'all',
        updatedAt: istDate.toISOString(),
      };

      // Only add specificClass if it has a value
      if (newEvent.specificClass) {
        eventData.specificClass = newEvent.specificClass;
      }

      // Update in Firestore
      const eventRef = doc(firestore, 'events', selectedEvent!.id);
      await updateDoc(eventRef, eventData);

      // Reset form
      setNewEvent({
        title: '',
        description: '',
        date: selectedDate,
        type: 'activity',
        targetAudience: 'all',
        specificClass: undefined,
      });

      setIsEditing(false);
      setShowAddModal(false);
      Alert.alert('Success', 'Event updated successfully');

      // Refresh events list
      fetchEvents();
    } catch (error) {
      console.error('Error updating event:', error);
      Alert.alert('Error', 'Failed to update event. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteEvent = (id: string) => {
    console.log('Delete event called with ID:', id);

    if (!id) {
      console.error('No event ID provided for deletion');
      Alert.alert('Error', 'Cannot delete event: No event ID found');
      return;
    }

    Alert.alert(
      'Confirm Delete',
      'Are you sure you want to delete this event?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              console.log('Starting delete operation for ID:', id);
              setLoading(true);

              // Delete from Firestore
              try {
                console.log('Creating document reference for collection "events" with ID:', id);
                const eventRef = doc(firestore, 'events', id);
                console.log('Event reference created, path:', eventRef.path);

                console.log('Attempting to delete document...');
                await deleteDoc(eventRef);
                console.log('Document deleted from Firestore successfully');
              } catch (deleteError: any) {
                console.error('Inner delete error details:', JSON.stringify(deleteError));
                if (deleteError && typeof deleteError === 'object') {
                  console.error('Error code:', deleteError.code);
                  console.error('Error message:', deleteError.message);
                }
                throw deleteError; // Re-throw to be caught by the outer catch
              }

              // Update local state
              setEvents(events.filter((event) => event.id !== id));
              setShowEventModal(false);
              Alert.alert('Success', 'Event deleted successfully');
            } catch (error) {
              console.error('Error deleting event:', error);
              Alert.alert('Error', `Failed to delete event: ${error instanceof Error ? error.message : 'Unknown error'}`);
            } finally {
              setLoading(false);
            }
          },
        },
      ]
    );
  };

  // This function can be used later if we need to filter events by date

  const getMarkedDates = () => {
    const marked: any = {};
    events.forEach((event) => {
      marked[event.date] = {
        marked: true,
        dotColor: getEventTypeColor(event.type),
        selected: event.date === selectedDate,
        selectedColor: 'rgba(59, 130, 246, 0.1)'
      };
    });
    return marked;
  };

  const getEventIcon = (type: string) => {
    switch (type) {
      case 'holiday':
        return 'calendar';
      case 'exam':
        return 'pencil';
      case 'activity':
        return 'star';
      default:
        return 'circle';
    }
  };

  return (
    <View className="flex-1 bg-gray-50">
      {/* Loading Overlay */}
      {loading && (
        <View className="absolute z-10 w-full h-full bg-black/30 items-center justify-center">
          <View className="bg-white p-4 rounded-xl shadow-lg">
            <ActivityIndicator size="large" color="#3B82F6" />
            <Text className="text-gray-700 mt-2 font-medium">Loading...</Text>
          </View>
        </View>
      )}

      {/* Header */}
      <View className="bg-white px-4 py-4 border-b border-gray-200">
        <Text className="text-xl sm:text-2xl font-bold text-gray-800">Calendar</Text>
        <Text className="text-xs sm:text-sm text-gray-500 mt-1">
          Manage school events and activities
        </Text>
      </View>

      {/* Calendar */}
      <View className="bg-white">
        <Calendar
          markedDates={getMarkedDates()}
          onDayPress={(day: DateData) => {
            setSelectedDate(day.dateString);
            const dayEvents = events.filter(e => e.date === day.dateString);
            if (dayEvents.length > 0) {
              setSelectedEvent(dayEvents[0]);
              setShowEventModal(true);
            }
          }}
          theme={{
            backgroundColor: '#ffffff',
            calendarBackground: '#ffffff',
            textSectionTitleColor: '#6B7280',
            selectedDayBackgroundColor: '#3B82F6',
            selectedDayTextColor: '#ffffff',
            todayTextColor: '#3B82F6',
            dayTextColor: '#1F2937',
            textDisabledColor: '#D1D5DB',
            dotColor: '#3B82F6',
            selectedDotColor: '#ffffff',
            arrowColor: '#3B82F6',
            monthTextColor: '#1F2937',
            textDayFontSize: 14,
            textMonthFontSize: 16,
            textDayHeaderFontSize: 12,
            'stylesheet.calendar.main': {
              week: {
                marginTop: 2,
                marginBottom: 2,
                flexDirection: 'row',
                justifyContent: 'space-around'
              }
            },
            'stylesheet.day.basic': {
              base: {
                width: 32,
                height: 32,
                alignItems: 'center',
                justifyContent: 'center'
              },
              dot: {
                width: 4,
                height: 4,
                marginTop: 1,
                borderRadius: 2
              }
            }
          }}
        />
      </View>

      {/* Event List */}
      <ScrollView className="flex-1 px-3 sm:px-4 py-4 sm:py-6" contentContainerStyle={{ paddingBottom: 120 }}>
        <View className="flex-row justify-between items-center mb-3">
          <Text className="text-base sm:text-lg font-semibold text-gray-800">
            Upcoming Events
          </Text>
          <Text className="text-xs text-blue-500">
            {events.filter(event => new Date(event.date) >= new Date()).length} events
          </Text>
        </View>

        {events.length === 0 && !loading ? (
          <View className="items-center justify-center py-8">
            <FontAwesome name="calendar-o" size={50} color="#D1D5DB" />
            <Text className="text-gray-400 mt-4 text-center">
              No events found. Tap the + button to add your first event.
            </Text>
          </View>
        ) : (
          <View className="space-y-4">
            {events
              .filter(event => new Date(event.date) >= new Date())
              .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())
              .map((event) => (
              <TouchableOpacity
                key={event.id}
                className="bg-white rounded-xl p-3 shadow-sm border border-gray-100"
                onPress={() => {
                  setSelectedEvent(event);
                  setShowEventModal(true);
                }}
                activeOpacity={0.7}
                style={{
                  borderLeftWidth: 4,
                  borderLeftColor: getEventTypeColor(event.type)
                }}
              >
                <View className="flex-row items-center mb-2">
                  <View className={`w-8 h-8 rounded-full items-center justify-center mr-2`}
                    style={{ backgroundColor: `${getEventTypeColor(event.type)}20` }}>
                    <FontAwesome
                      name={getEventIcon(event.type)}
                      size={16}
                      color={getEventTypeColor(event.type)}
                    />
                  </View>
                  <Text className="text-base font-bold text-gray-800 flex-1 flex-wrap">
                    {event.title}
                  </Text>
                </View>

                <View className="flex-row justify-between items-center">
                  <View className="flex-row items-center">
                    <FontAwesome name="calendar" size={12} color="#6B7280" style={{ marginRight: 4 }} />
                    <Text className="text-sm text-gray-500">
                      {new Date(event.date).toLocaleDateString('en-IN', {
                        day: 'numeric',
                        month: 'short',
                        year: 'numeric'
                      })}
                    </Text>
                  </View>

                  <View className="bg-gray-100 px-2 py-1 rounded-full">
                    <Text className="text-xs text-gray-600 capitalize">
                      {event.targetAudience === 'specific_class' ? 'Class' : event.targetAudience}
                    </Text>
                  </View>
                </View>
              </TouchableOpacity>
            ))}
          </View>
        )}
      </ScrollView>

      {/* Add Event Button */}
      <Animated.View
        style={{
          position: 'absolute',
          right: 24,
          bottom: 110, // Ensure it's above the tab bar on all devices
          zIndex: 1000,
          transform: [{ scale: scaleAnim }],
        }}
      >
        <TouchableOpacity
          className="bg-blue-500 rounded-full items-center justify-center shadow-xl"
          onPress={() => setShowAddModal(true)}
          style={{
            elevation: 10,
            shadowColor: '#3B82F6',
            shadowOffset: { width: 0, height: 5 },
            shadowOpacity: 0.4,
            shadowRadius: 8,
            width: 70,
            height: 70,
          }}
          activeOpacity={0.7}
        >
          <View className="w-full h-full items-center justify-center">
            <FontAwesome name="plus" size={32} color="white" />
          </View>
        </TouchableOpacity>
      </Animated.View>

      {/* Add Event Modal */}
      <Modal
        visible={showAddModal}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setShowAddModal(false)}
      >
        <View className="flex-1 bg-black/50 justify-end">
          <View className="bg-white rounded-t-3xl p-4 sm:p-6 max-h-[90%] overflow-y-auto">
            <View className="flex-row justify-between items-center mb-6">
              <Text className="text-xl font-bold text-gray-800">
                Add New Event
              </Text>
              <TouchableOpacity
                onPress={() => setShowAddModal(false)}
                className="p-2"
              >
                <FontAwesome name="close" size={24} color="#6B7280" />
              </TouchableOpacity>
            </View>

            <View className="space-y-4">
              <View>
                <Text className="text-sm font-medium text-gray-700 mb-1">
                  Event Title
                </Text>
                <View className="relative">
                  <TextInput
                    className="w-full bg-gray-50 border border-gray-200 rounded-xl px-4 py-3 text-gray-900 pl-10"
                    placeholder="Enter event title"
                    placeholderTextColor="#9CA3AF"
                    value={newEvent.title}
                    onChangeText={(text) => setNewEvent({...newEvent, title: text})}
                  />
                  <View className="absolute left-3 top-3">
                    <FontAwesome name="pencil" size={18} color="#3B82F6" />
                  </View>
                </View>
              </View>

              <View>
                <Text className="text-sm font-medium text-gray-700 mb-1">
                  Description
                </Text>
                <View className="relative">
                  <TextInput
                    className="w-full bg-gray-50 border border-gray-200 rounded-xl px-4 py-3 text-gray-900 pl-10"
                    placeholder="Enter event description"
                    multiline
                    numberOfLines={4}
                    textAlignVertical="top"
                    style={{ minHeight: 100 }}
                    placeholderTextColor="#9CA3AF"
                    value={newEvent.description}
                    onChangeText={(text) => setNewEvent({...newEvent, description: text})}
                  />
                  <View className="absolute left-3 top-3">
                    <FontAwesome name="align-left" size={18} color="#3B82F6" />
                  </View>
                </View>
              </View>

              <View>
                <Text className="text-sm font-medium text-gray-700 mb-1">
                  Date
                </Text>
                <TouchableOpacity
                  className="w-full bg-gray-50 border border-gray-200 rounded-xl px-4 py-3 mb-4 flex-row justify-between items-center"
                  onPress={() => {
                    // Show calendar picker in modal
                    setShowDatePicker(true);
                  }}
                >
                  <Text className="text-gray-900 font-medium">
                    {new Date(newEvent.date || selectedDate).toLocaleDateString('en-IN', {
                      weekday: 'long',
                      day: 'numeric',
                      month: 'long',
                      year: 'numeric'
                    })}
                  </Text>
                  <FontAwesome name="calendar" size={20} color="#3B82F6" />
                </TouchableOpacity>
              </View>

              <View>
                <Text className="text-sm font-medium text-gray-700 mb-1">
                  Event Type
                </Text>
                <View className="flex-row flex-wrap gap-2">
                  {['holiday', 'exam', 'activity', 'other'].map((type) => {
                    const isSelected = newEvent.type === type;
                    const getTypeColor = () => {
                      switch(type) {
                        case 'holiday': return '#EF4444';
                        case 'exam': return '#F59E0B';
                        case 'activity': return '#10B981';
                        default: return '#6B7280';
                      }
                    };
                    const color = getTypeColor();
                    return (
                      <TouchableOpacity
                        key={type}
                        className={`flex-1 min-w-[70px] p-3 rounded-xl border ${isSelected ? 'border-2' : 'border'} ${
                          type === 'holiday' ? 'border-red-500 bg-red-50' :
                          type === 'exam' ? 'border-yellow-500 bg-yellow-50' :
                          type === 'activity' ? 'border-green-500 bg-green-50' :
                          'border-gray-500 bg-gray-50'
                        }`}
                        onPress={() => {
                          setNewEvent({...newEvent, type: type as 'holiday' | 'exam' | 'activity' | 'other'});
                        }}
                        style={isSelected ? { shadowColor: color, shadowOffset: { width: 0, height: 2 }, shadowOpacity: 0.3, shadowRadius: 3, elevation: 3 } : {}}
                      >
                        <View className="flex-row justify-center items-center">
                          {isSelected && (
                            <FontAwesome
                              name="check-circle"
                              size={16}
                              color={color}
                              style={{ marginRight: 6 }}
                            />
                          )}
                          <Text className={`text-center text-sm capitalize font-medium ${
                            type === 'holiday' ? 'text-red-700' :
                            type === 'exam' ? 'text-yellow-700' :
                            type === 'activity' ? 'text-green-700' :
                            'text-gray-700'
                          }`}>
                            {type}
                          </Text>
                        </View>
                      </TouchableOpacity>
                    );
                  })}
                </View>
              </View>

              <View>
                <Text className="text-sm font-medium text-gray-700 mb-1">
                  Target Audience
                </Text>
                <View className="flex-row flex-wrap gap-2">
                  {['all', 'parents', 'teachers', 'specific_class'].map((audience) => {
                    const isSelected = newEvent.targetAudience === audience;
                    const displayName = audience === 'specific_class' ? 'Specific Class' : audience.charAt(0).toUpperCase() + audience.slice(1);

                    return (
                      <TouchableOpacity
                        key={audience}
                        className={`flex-1 min-w-[80px] p-3 rounded-xl border ${isSelected ? 'border-2 border-blue-500' : 'border-gray-200'} ${isSelected ? 'bg-blue-50' : 'bg-gray-50'}`}
                        onPress={() => {
                          setNewEvent({...newEvent, targetAudience: audience as 'all' | 'parents' | 'teachers' | 'specific_class'});
                          setShowClassInput(audience === 'specific_class');
                        }}
                      >
                        <View className="flex-row justify-center items-center">
                          {isSelected && (
                            <FontAwesome
                              name="check-circle"
                              size={16}
                              color="#3B82F6"
                              style={{ marginRight: 6 }}
                            />
                          )}
                          <Text className={`text-center text-sm font-medium ${isSelected ? 'text-blue-700' : 'text-gray-700'}`}>
                            {displayName}
                          </Text>
                        </View>
                      </TouchableOpacity>
                    );
                  })}
                </View>
              </View>

              {/* Show class input only when specific_class is selected */}
              {newEvent.targetAudience === 'specific_class' && (
                <View>
                  <Text className="text-sm font-medium text-gray-700 mb-1">
                    Class Name
                  </Text>
                  <View className="relative">
                    <TextInput
                      className="w-full bg-gray-50 border border-gray-200 rounded-xl px-4 py-3 text-gray-900 pl-10"
                      placeholder="Enter class name (e.g. Class 5-A)"
                      placeholderTextColor="#9CA3AF"
                      value={newEvent.specificClass}
                      onChangeText={(text) => setNewEvent({...newEvent, specificClass: text})}
                    />
                    <View className="absolute left-3 top-3">
                      <FontAwesome name="users" size={18} color="#3B82F6" />
                    </View>
                  </View>
                </View>
              )}

              <TouchableOpacity
                className="w-full bg-blue-500 p-4 rounded-xl mt-6 shadow-md"
                onPress={isEditing ? handleUpdateEvent : handleAddEvent}
                activeOpacity={0.8}
                style={{
                  shadowColor: '#3B82F6',
                  shadowOffset: { width: 0, height: 4 },
                  shadowOpacity: 0.3,
                  shadowRadius: 4,
                  elevation: 5
                }}
              >
                <View className="flex-row justify-center items-center">
                  <FontAwesome
                    name={isEditing ? "edit" : "calendar-plus-o"}
                    size={20}
                    color="white"
                    style={{ marginRight: 8 }}
                  />
                  <Text className="text-white text-center font-bold text-lg">
                    {isEditing ? 'Update Event' : 'Add Event'}
                  </Text>
                </View>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

      {/* Date Picker Modal */}
      <Modal
        visible={showDatePicker}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setShowDatePicker(false)}
      >
        <View className="flex-1 bg-black/50 justify-end">
          <View className="bg-white rounded-t-3xl p-4 sm:p-6 max-h-[90%] overflow-y-auto">
            <View className="flex-row justify-between items-center mb-6">
              <Text className="text-xl font-bold text-gray-800">
                Select Date
              </Text>
              <TouchableOpacity
                onPress={() => setShowDatePicker(false)}
                className="p-2"
              >
                <FontAwesome name="close" size={24} color="#6B7280" />
              </TouchableOpacity>
            </View>

            <Calendar
              current={newEvent.date || selectedDate}
              onDayPress={(day: DateData) => {
                setNewEvent({...newEvent, date: day.dateString});
                setShowDatePicker(false);
              }}
              markedDates={{
                [newEvent.date || selectedDate]: {selected: true, selectedColor: '#3B82F6'}
              }}
              theme={{
                backgroundColor: '#ffffff',
                calendarBackground: '#ffffff',
                textSectionTitleColor: '#6B7280',
                selectedDayBackgroundColor: '#3B82F6',
                selectedDayTextColor: '#ffffff',
                todayTextColor: '#3B82F6',
                dayTextColor: '#1F2937',
                textDisabledColor: '#D1D5DB',
                dotColor: '#3B82F6',
                selectedDotColor: '#ffffff',
                arrowColor: '#3B82F6',
                monthTextColor: '#1F2937',
                textDayFontSize: 16,
                textMonthFontSize: 16,
                textDayHeaderFontSize: 14
              }}
            />

            <TouchableOpacity
              className="w-full bg-blue-500 p-4 rounded-xl mt-6 shadow-md"
              onPress={() => setShowDatePicker(false)}
              activeOpacity={0.8}
              style={{
                shadowColor: '#3B82F6',
                shadowOffset: { width: 0, height: 4 },
                shadowOpacity: 0.3,
                shadowRadius: 4,
                elevation: 5
              }}
            >
              <View className="flex-row justify-center items-center">
                <FontAwesome name="check-circle" size={20} color="white" style={{ marginRight: 8 }} />
                <Text className="text-white text-center font-bold text-lg">
                  Confirm Date
                </Text>
              </View>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>

      {/* Event Details Modal */}
      <Modal
        visible={showEventModal}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setShowEventModal(false)}
      >
        <View className="flex-1 bg-black/50 justify-end">
          <View className="bg-white rounded-t-3xl p-4 sm:p-6 max-h-[90%] overflow-y-auto">
            <View className="flex-row justify-between items-center mb-6">
              <Text className="text-xl font-bold text-gray-800">
                Event Details
              </Text>
              <TouchableOpacity
                onPress={() => setShowEventModal(false)}
                className="p-2"
              >
                <FontAwesome name="close" size={24} color="#6B7280" />
              </TouchableOpacity>
            </View>

            {selectedEvent && (
              <View className="space-y-6">
                <View>
                  <Text className="text-sm font-medium text-gray-500">
                    Event Title
                  </Text>
                  <Text className="text-lg font-semibold text-gray-800 mt-1">
                    {selectedEvent.title}
                  </Text>
                </View>

                <View>
                  <Text className="text-sm font-medium text-gray-500">
                    Description
                  </Text>
                  <Text className="text-gray-800 mt-1">
                    {selectedEvent.description}
                  </Text>
                </View>

                <View className="flex-row justify-between items-center">
                  <View>
                    <Text className="text-sm font-medium text-gray-500">
                      Date
                    </Text>
                    <Text className="text-gray-800 mt-1">
                      {new Date(selectedEvent.date).toLocaleDateString('en-IN', {
                        weekday: 'long',
                        day: 'numeric',
                        month: 'long',
                        year: 'numeric'
                      })}
                    </Text>
                  </View>
                  <View className={`px-4 py-2 rounded-full`}
                    style={{ backgroundColor: `${getEventTypeColor(selectedEvent.type)}20` }}>
                    <Text className="text-sm font-medium capitalize"
                      style={{ color: getEventTypeColor(selectedEvent.type) }}>
                      {selectedEvent.type}
                    </Text>
                  </View>
                </View>

                <View className="flex-row space-x-8 mt-6">
                  <TouchableOpacity
                    className="flex-1 bg-blue-500 p-5 rounded-xl shadow-md"
                    style={{ elevation: 3 }}
                    onPress={() => handleEditEvent(selectedEvent)}
                    activeOpacity={0.7}
                  >
                    <Text className="text-white text-center font-bold text-base">
                      Edit Event
                    </Text>
                  </TouchableOpacity>
                  <TouchableOpacity
                    className="flex-1 bg-red-500 p-5 rounded-xl shadow-md"
                    style={{ elevation: 3 }}
                    activeOpacity={0.7}
                    onPress={async () => {
                      try {
                        console.log('Direct delete button pressed, event ID:', selectedEvent.id);
                        setLoading(true);

                        // Create reference and delete directly
                        const eventRef = doc(firestore, 'events', selectedEvent.id);
                        await deleteDoc(eventRef);

                        // Update local state
                        setEvents(events.filter(event => event.id !== selectedEvent.id));
                        setShowEventModal(false);
                        Alert.alert('Success', 'Event deleted successfully');
                      } catch (error) {
                        console.error('Direct delete error:', error);
                        Alert.alert('Error', 'Failed to delete event. Please try again.');
                      } finally {
                        setLoading(false);
                      }
                    }}
                  >
                    <Text className="text-white text-center font-bold text-base">
                      Delete Event
                    </Text>
                  </TouchableOpacity>
                </View>
              </View>
            )}
          </View>
        </View>
      </Modal>
    </View>
  );
}